import { useState } from 'react';

/**
 * 计算屏幕最大容纳条数的通用 hook
 * @param {Object} options - 配置选项
 * @param {number} options.cardWidth - 卡片宽度（包含间距），默认 300
 * @param {number} options.cardHeight - 卡片高度（包含间距），默认 220
 * @param {number} options.headerHeight - 页面头部高度，默认 200
 * @param {number} options.paginationHeight - 分页组件高度，默认 80
 * @param {number} options.pageMargin - 页面左右边距，默认 40
 * @param {number} options.minItems - 最少条数，默认 15
 * @param {string} options.pageName - 页面名称（用于日志），默认 '页面'
 * @returns {number} 计算出的页面大小
 */
const usePageSize = (options = {}) => {
    const {
        cardWidth = 300,
        cardHeight = 220,
        headerHeight = 200,
        paginationHeight = 80,
        pageMargin = 40,
        minItems = 15,
        pageName = '页面'
    } = options;

    const [pageSize] = useState(() => {
        const screenWidth = window.innerWidth;
        const screenHeight = window.innerHeight;

        console.log(`${pageName} - 屏幕尺寸:`, screenWidth, 'x', screenHeight);

        // 估算卡片网格布局能容纳的最大条数
        const availableWidth = screenWidth - pageMargin; // 减去页面左右边距
        const availableHeight = screenHeight - headerHeight - paginationHeight;

        const cols = Math.floor(availableWidth / cardWidth);
        const rows = Math.floor(availableHeight / cardHeight);

        const maxItems = Math.max(cols * rows, minItems); // 最少 minItems 条

        console.log(`${pageName}计算结果: ${cols}列 x ${rows}行 = ${maxItems}条`);

        return maxItems;
    });

    return pageSize;
};

export default usePageSize;
