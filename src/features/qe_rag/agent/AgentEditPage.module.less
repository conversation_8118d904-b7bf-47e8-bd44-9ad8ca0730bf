// AgentEditPage 独立样式文件
.editContainer {
    height: 100vh;
    background: #f3f2fe87;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 固定头部区域 */
.fixedHeader {
    flex-shrink: 0;
    padding: 24px 24px 0 24px;
    background: #f3f2fe53;
    backdrop-filter: blur(10px);
}

/* 可滚动内容区域 */
.scrollableContent {
    flex: 1;
    overflow: hidden;
    background: #f5f5f5;
    position: relative;

    /* Loading 状态样式 */
    :global(.ant-spin-container) {
        /* min-height: 400px; 这个属性得注释 */
    }

    :global(.ant-spin-spinning) {
        :global(.ant-spin-container) {
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}

.cardContainer {
    display: flex;
    flex: 1;
    min-height: 0;
    gap: 24px;
    overflow: hidden;
    max-height: calc(100vh - 130px);
    padding: 0 24px;
}

/* 左侧面板 - 应用设定 */
.leftPanel {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

.leftPanelHeader {
    height: 72px;
    padding: 16px 24px;
    background: #ffffff;
    color: #1a1a1a;
    border-bottom: 1px solid #e8e8e8;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.panelTitle {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    display: flex;
    align-items: center;
    gap: 8px;
    line-height: 1.2;
    
}

.panelSubtitle {
    margin-top: 2px;
    font-size: 12px;
    color: #666666;
    font-weight: 400;
    line-height: 1.2;
}

.leftPanelContent {
    flex: 1;
    overflow-y: auto;
    background: #fafafa;
    
    /* 自定义滚动条 */
    &::-webkit-scrollbar {
        width: 6px;
    }
    
    &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
        
        &:hover {
            background: #a8a8a8;
        }
    }
}

/* 右侧面板 - Agent 预览 */
.rightPanel {
    flex: 1.2;
    display: flex;
    flex-direction: column;
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

.rightPanelHeader {
    height: 72px;
    padding: 16px 24px;
    background: #ffffff;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
}

.agentInfo {
    display: flex;
    align-items: center;
    gap: 16px;
}

.agentAvatar {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    overflow: hidden;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #e8e8e8;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.avatarImage {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 14px;
}

.avatarPlaceholder {
    font-size: 20px;
    color: #666666;
}

.agentDetails {
    flex: 1;
    color: #1a1a1a;
}

.agentNameRow {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 6px;
}

.agentName {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    line-height: 1.2;
}

.agentStatus {
    display: flex;
    align-items: center;
    gap: 4px;
}

.statusDot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #52c41a;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

.statusText {
    font-size: 12px;
    color: #52c41a;
    font-weight: 500;
}

.agentDescription {
    font-size: 12px;
    color: #666666;
    line-height: 1.3;
    max-width: 280px;
}

.rightPanelContent {
    flex: 1;
    overflow: hidden;
    background: #ffffff;
    
    :global(.ant-card-body) {
        padding: 0;
        height: 100%;
        overflow: hidden;
    }
}