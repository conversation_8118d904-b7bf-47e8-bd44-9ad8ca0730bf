import { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'umi';
import { Card, message } from 'antd';
import EllipsisTooltip from 'COMMON/components/EllipsisTooltip';
import AgentHeader from './components/AgentHeader';
import AgentConfig from './components/AgentConfig';
import AgentPreview from './components/AgentPreview';
import AgentTable from './components/AgentTable';
import MCPServerDialog from './components/MCPServerDialog';
import { saveAgent, getAgentById, updateAgent } from 'COMMON/api/qe_rag/agent';
import { getChatModels, getEmbddingModels } from 'COMMON/api/qe_rag/model';
import { getMcpServerListByGroup } from 'COMMON/api/qe_rag/mcpServers';
import styles from './AgentEditPage.module.less';
import { ToolFilled } from '@ant-design/icons';

const AgentEditPage = () => {
    const [searchParams] = useSearchParams();
    const agentId = searchParams.get('id');

    const [agentData, setAgentData] = useState({
        id: 0,
        imageUrl: '', // 图片url
        name: '', // agent 名称
        description: '', // 描述
        groupId: 0, // 工作组ID
        responsePrompt: '',
        rulePrompt: '',
        rolePrompt: 'qa',
        recallCount: 1,
        similarity: 0.1,
        modelId: '',
        embeddingModelId: '',
        selectedRows: [],
        knowledgeBaseIds: '',
        mcpServerIds: '',
        selectedMcpServers: [],
        flowNodes: [] // MCP服务器节点
    });

    const [dialogTableVisible, setDialogTableVisible] = useState(false);
    const [mcpServerDialogVisible, setMcpServerDialogVisible] = useState(false);
    const [isLoadingFaBu, setIsLoadingFaBu] = useState(true);
    const [isLoadingUpdate, setIsLoadingUpdate] = useState(false);
    const [modelOptions, setModelOptions] = useState([]);
    const [embeddingModelOptions, setEmbeddingModelOptions] = useState([]);
    const [mcpServers, setMcpServers] = useState([]);
    const [selectedServersInOrder, setSelectedServersInOrder] = useState([]); // 按选中顺序存储服务器信息
    const [nextOrder, setNextOrder] = useState(1); // 下一个序号
    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false); // 是否有未保存的更改
    const [functionCallSupported, setFunctionCallSupported] = useState(false); // 当前模型是否支持函数调用

    const baseURL = window.location.origin + '/rag/api/agent/image/upload';

    const getModels = useCallback(async () => {
        try {
            const resp = await getChatModels();
            if (resp && Array.isArray(resp)) {
                // API直接返回数组
                setModelOptions(resp);
            } else if (resp && resp.code === 200) {
                // API返回包装的数据结构
                setModelOptions(resp.data);
            }
        } catch (error) {
            console.error('获取模型列表失败:', error);
        }
    }, []);

    const getEmbeddingModels = useCallback(async () => {
        try {
            const resp = await getEmbddingModels();
            if (resp && Array.isArray(resp)) {
                // API直接返回数组
                setEmbeddingModelOptions(resp);
            } else if (resp && resp.code === 200) {
                // API返回包装的数据结构
                setEmbeddingModelOptions(resp.data);
            }
        } catch (error) {
            console.error('获取切词模型列表失败:', error);
        }
    }, []);

    const loadAgentData = useCallback(async (id) => {
        try {
            const res = await getAgentById(id);

            // 根据实际的API响应结构来处理数据
            if (res && res.similarity !== undefined) {
                // API直接返回数据对象
                setIsLoadingFaBu(false);
                setIsLoadingUpdate(true);
                setAgentData((prev) => ({
                    ...prev,
                    similarity: res.similarity,
                    recallCount: res.recallCount,
                    name: res.name,
                    description: res.description,
                    imageUrl: res.url,
                    modelId: res.modelId,
                    embeddingModelId: res.embeddingModelId || '',
                    knowledgeBaseIds: res.knowledgeBaseIds,
                    selectedRows: res.knowledgeBaseList || [],
                    rulePrompt: res.rulePrompt,
                    rolePrompt: res.rolePrompt,
                    responsePrompt: res.responsePrompt,
                    groupId: res.groupId,
                    flowNodes: res.flowNodes || []
                }));
            } else if (res && res.code === 200 && res.data) {
                // API返回包装的数据结构
                setIsLoadingFaBu(false);
                setIsLoadingUpdate(true);
                setAgentData((prev) => ({
                    ...prev,
                    similarity: res.data.similarity,
                    recallCount: res.data.recallCount,
                    name: res.data.name,
                    description: res.data.description,
                    imageUrl: res.data.url,
                    modelId: res.data.modelId,
                    embeddingModelId: res.data.embeddingModelId || '',
                    knowledgeBaseIds: res.data.knowledgeBaseIds,
                    selectedRows: res.data.knowledgeBaseList || [],
                    rulePrompt: res.data.rulePrompt,
                    rolePrompt: res.data.rolePrompt,
                    responsePrompt: res.data.responsePrompt,
                    groupId: res.data.groupId,
                    flowNodes: res.data.flowNodes || []
                }));
            }
        } catch (error) {
            console.error('加载Agent数据失败:', error);
        }
    }, []);

    const initData = useCallback(async () => {
        await Promise.all([getModels(), getEmbeddingModels()]);

        const id = agentId ? parseInt(agentId, 10) || 0 : 0;
        setAgentData((prev) => ({ ...prev, id }));

        if (id !== 0) {
            await loadAgentData(id);
        }
    }, [agentId, getModels, getEmbeddingModels, loadAgentData]);

    useEffect(() => {
        initData();
    }, [initData]);

    // 监听模型变化，更新 functionCallSupported 状态
    useEffect(() => {
        if (!agentData.modelId || !modelOptions.length) {
            setFunctionCallSupported(false);
            return;
        }

        const currentModel = modelOptions.find(
            (model) => model.id.toString() === agentData.modelId.toString()
        );
        const supported = currentModel && currentModel.functionCall === 1;
        setFunctionCallSupported(supported);
    }, [agentData.modelId, modelOptions]);

    const handleSelectedData = (updateValue) => {
        setAgentData((prev) => ({
            ...prev,
            selectedRows: updateValue,
            knowledgeBaseIds: updateValue.map((item) => item.id).join(';')
        }));
        setHasUnsavedChanges(true); // 标记有未保存的更改
    };

    const handleOpenDialog = () => {
        setDialogTableVisible(true);
    };

    const handleOpenMCPServerDialog = async () => {
        await fetchMCPServers();
        setMcpServerDialogVisible(true);
    };

    const fetchMCPServers = async () => {
        try {
            const res = await getMcpServerListByGroup(agentData.groupId);
            if (res && Array.isArray(res)) {
                // API直接返回数组
                setMcpServers(
                    res.map((server) => ({
                        id: server.id,
                        name: server.name,
                        description: server.description || ''
                    }))
                );
            } else if (res && res.code === 200) {
                // API返回包装的数据结构
                setMcpServers(
                    res.data.map((server) => ({
                        id: server.id,
                        name: server.name,
                        description: server.description || ''
                    }))
                );
            }
        } catch (error) {
            console.error('获取MCP服务器列表异常:', error);
        }
    };

    const initSelectedMCPServers = useCallback(() => {
        setSelectedServersInOrder([]);
        setNextOrder(1);

        if (agentData.flowNodes && agentData.flowNodes.length > 0) {
            const sortedNodes = [...agentData.flowNodes].sort((a, b) => a.numbering - b.numbering);
            const newSelectedServers = [];
            let order = 1;

            sortedNodes.forEach((node) => {
                newSelectedServers.push({
                    id: node.mcpServerId,
                    order: order++,
                    description: node.description || ''
                });
            });

            setSelectedServersInOrder(newSelectedServers);
            setNextOrder(order);
        }
    }, [agentData.flowNodes]);

    const checkData = () => {
        // 必填字段验证
        if (!agentData.name?.trim()) {
            message.error('智能体标题不能为空');
            return false;
        }

        if (!agentData.groupId) {
            message.error('工作组不能为空，请选择工作组');
            return false;
        }

        if (!agentData.modelId) {
            message.error('生成模型不能为空，请选择模型');
            return false;
        }

        // 其他可选验证
        if (agentData.recallCount && (agentData.recallCount < 1 || agentData.recallCount > 10)) {
            message.error('召回数必须在1-10之间');
            return false;
        }

        if (agentData.similarity && (agentData.similarity < 0.1 || agentData.similarity > 1)) {
            message.error('相似度必须在0.1-1之间');
            return false;
        }

        return true;
    };

    const prepareRequestData = () => {
        const {
            name,
            description = '',
            imageUrl: url = '',
            knowledgeBaseIds = '',
            recallCount = 1,
            similarity = 0.1,
            modelId,
            embeddingModelId = '',
            groupId,
            rolePrompt = 'qa',
            responsePrompt = '',
            rulePrompt = '',
            flowNodes = []
        } = agentData;

        // 对 flowNodes 重新进行排序和编号
        const sortedFlowNodes = [...flowNodes].sort((a, b) => a.numbering - b.numbering);
        const reNumberedFlowNodes = sortedFlowNodes.map((node, index) => ({
            ...node,
            numbering: index
        }));

        // 获取模型名称
        let modelName = '';
        if (modelId && modelOptions) {
            const model = modelOptions.find((m) => m.id === modelId);
            if (model) {
                modelName = model.name;
            }
        }

        return {
            name,
            description,
            url,
            knowledgeBaseIds,
            recallCount,
            similarity,
            modelId,
            embeddingModelId,
            modelName,
            groupId,
            rolePrompt,
            responsePrompt: responsePrompt,
            rulePrompt,
            flowNodes: reNumberedFlowNodes.map((node) => ({
                ...(node.id ? { id: node.id } : {}), // 如果存在id则包含，否则忽略
                mcpServerId: node.mcpServerId,
                mcpServerName: node.mcpServerName || '',
                numbering: node.numbering,
                inputLimit: node.inputLimit || '',
                outputLimit: node.outputLimit || ''
            }))
        };
    };

    const toggleServer = (serverId, checked) => {
        if (checked) {
            // 添加服务器到选择列表
            const server = mcpServers.find((s) => s.id === serverId);
            setSelectedServersInOrder((prev) => [
                ...prev,
                {
                    id: serverId,
                    order: nextOrder,
                    description: server.description || ''
                }
            ]);
            setNextOrder((prev) => prev + 1);
        } else {
            // 从选择列表中移除服务器
            const index = selectedServersInOrder.findIndex((item) => item.id === serverId);
            if (index !== -1) {
                const newSelected = [...selectedServersInOrder];
                newSelected.splice(index, 1);
                setSelectedServersInOrder(newSelected);
                // 重新排序
                reorderSelectedServers(newSelected);
            }
        }
    };

    const reorderSelectedServers = (servers = selectedServersInOrder) => {
        // 重新排序，确保序号连续
        const sorted = servers.sort((a, b) => a.order - b.order);
        // 更新序号
        const reordered = sorted.map((item, index) => ({
            ...item,
            order: index + 1
        }));

        setSelectedServersInOrder(reordered);
        setNextOrder(reordered.length + 1);
    };

    const confirmMCPServerSelection = (selectedServers) => {
        const newFlowNodes = selectedServers.map((item, index) => {
            const server = mcpServers.find((s) => s.id === item.id);

            // 检查是否是已有节点
            const existingNode = agentData.flowNodes
                ? agentData.flowNodes.find((node) => node.mcpServerId === item.id)
                : null;

            if (existingNode) {
                // 如果是已有节点，保留其ID和其他信息，更新numbering
                return {
                    ...existingNode,
                    numbering: index,
                    description: server?.description || ''
                };
            } else {
                // 如果是新节点
                return {
                    mcpServerId: item.id,
                    mcpServerName: server?.name || '',
                    numbering: index,
                    description: server?.description || ''
                };
            }
        });

        setAgentData((prev) => ({ ...prev, flowNodes: newFlowNodes }));
        setHasUnsavedChanges(true); // 标记有未保存的更改
    };

    // 监听 MCP 对话框打开时初始化已选服务器
    useEffect(() => {
        if (mcpServerDialogVisible) {
            initSelectedMCPServers();
        }
    }, [mcpServerDialogVisible, initSelectedMCPServers]);

    // 监听切词模型变化时清空已选择的知识库
    const [prevEmbeddingModelId, setPrevEmbeddingModelId] = useState('');

    useEffect(() => {
        // 只有在非初始化状态下且确实发生变化时才清空已选择的知识库
        if (
            prevEmbeddingModelId &&
            prevEmbeddingModelId !== '' &&
            agentData.embeddingModelId !== prevEmbeddingModelId
        ) {
            setAgentData((prev) => ({
                ...prev,
                selectedRows: [],
                knowledgeBaseIds: ''
            }));
            setHasUnsavedChanges(true);
        }
        setPrevEmbeddingModelId(agentData.embeddingModelId);
    }, [agentData.embeddingModelId, prevEmbeddingModelId]);

    // 监听数据变化，设置未保存状态
    useEffect(() => {
        // 如果是新建模式（id为0）且有数据变化，或者是编辑模式且数据有变化
        if (agentData.name || agentData.description || agentData.modelId) {
            setHasUnsavedChanges(true);
        }
    }, [
        agentData.name,
        agentData.description,
        agentData.modelId,
        agentData.embeddingModelId,
        agentData.groupId
    ]);

    // 保存成功后重置未保存状态
    const saveAgentHandler = async () => {
        if (!checkData()) {
            return;
        }

        try {
            const requestData = prepareRequestData();
            const response = await saveAgent(requestData);

            if (response && typeof response === 'number') {
                // API直接返回新创建的ID
                message.success('创建智能体成功');
                setAgentData((prev) => ({ ...prev, id: response }));
                setIsLoadingUpdate(true);
                setIsLoadingFaBu(false);
                setHasUnsavedChanges(false);
            } else if (response && response.code === 200) {
                // API返回包装的数据结构
                message.success('创建智能体成功');
                const newAgentId = parseInt(response.data);
                setAgentData((prev) => ({ ...prev, id: newAgentId }));
                setIsLoadingUpdate(true);
                setIsLoadingFaBu(false);
                setHasUnsavedChanges(false);
            } else {
                const errorMessage = response?.message || '创建失败';
                message.error(errorMessage);
            }
        } catch (error) {
            console.error('创建智能体出错:', error);
            message.error('创建智能体时发生错误，请稍后重试');
        }
    };

    const updateAgentHandler = async () => {
        if (!checkData()) {
            return;
        }
        if (!agentData.id) {
            message.error('缺少智能体ID，无法更新');
            return;
        }

        try {
            const baseParams = prepareRequestData();
            const requestData = {
                ...baseParams,
                id: agentData.id
            };
            const response = await updateAgent(requestData);

            if (response && typeof response === 'string') {
                // API直接返回成功消息字符串
                message.success(response || '更新智能体成功');
                setHasUnsavedChanges(false);
            } else if (response && response.code === 200) {
                // API返回包装的数据结构
                message.success(response.data || '更新智能体成功');
                setHasUnsavedChanges(false);
            } else if (response === true || response === undefined) {
                // API返回布尔值或无返回值表示成功
                message.success('更新智能体成功');
                setHasUnsavedChanges(false);
            } else {
                const errorMessage = response?.message || '更新失败';
                message.error(errorMessage);
            }
        } catch (error) {
            console.error('更新智能体出错:', error);
            message.error('更新智能体时发生错误，请稍后重试');
        }
    };

    return (
        <div className={styles.editContainer}>
            <div className={styles.fixedHeader}>
                <AgentHeader
                    isLoadingFaBu={isLoadingFaBu}
                    isLoadingUpdate={isLoadingUpdate}
                    onSave={saveAgentHandler}
                    onUpdate={updateAgentHandler}
                    hasUnsavedChanges={hasUnsavedChanges}
                />
            </div>

            <div className={styles.scrollableContent}>
                {/* 知识库关联对话框 */}
                <AgentTable
                    visible={dialogTableVisible}
                    onClose={() => setDialogTableVisible(false)}
                    groupId={agentData.groupId}
                    embeddingModelId={agentData.embeddingModelId}
                    selectedKnowledgeBases={agentData.selectedRows}
                    onUpdateValue={handleSelectedData}
                />

                {/* MCP服务器关联对话框 */}
                <MCPServerDialog
                    visible={mcpServerDialogVisible}
                    onClose={() => setMcpServerDialogVisible(false)}
                    mcpServers={mcpServers}
                    selectedServersInOrder={selectedServersInOrder}
                    onConfirm={confirmMCPServerSelection}
                />

                <div className={styles.cardContainer}>
                    <div className={styles.leftPanel}>
                        <div className={styles.leftPanelHeader}>
                            <h2 className={styles.panelTitle}>应用设定</h2>
                            <div className={styles.panelSubtitle}>配置您的 AI Agent 参数</div>
                        </div>
                        <div className={styles.leftPanelContent}>
                            <AgentConfig
                                baseURL={baseURL}
                                agentData={agentData}
                                modelOptions={modelOptions}
                                embeddingModelOptions={embeddingModelOptions}
                                functionCallSupported={functionCallSupported}
                                onUpdateAgentData={(newData) => {
                                    setAgentData((prev) => ({ ...prev, ...newData }));
                                    setHasUnsavedChanges(true); // 标记有未保存的更改
                                }}
                                onOpenAssociationDialog={handleOpenDialog}
                                onOpenMCPServerDialog={handleOpenMCPServerDialog}
                            />
                        </div>
                    </div>

                    <div className={styles.rightPanel}>
                        <div className={styles.rightPanelHeader}>
                            <div className={styles.agentInfo}>
                                <div className={styles.agentAvatar}>
                                    {agentData.imageUrl ? (
                                        <img
                                            src={agentData.imageUrl}
                                            alt="Agent Avatar"
                                            className={styles.avatarImage}
                                        />
                                    ) : (
                                        <div className={styles.avatarPlaceholder}>🤖</div>
                                    )}
                                </div>
                                <div className={styles.agentDetails}>
                                    <div className={styles.agentNameRow}>
                                        <h3 className={styles.agentName}>
                                            {agentData.name || '我的Agent'}
                                        </h3>
                                        <div className={styles.agentStatus}>
                                            <span className={styles.statusDot}></span>
                                            <span className={styles.statusText}>预览和调试</span>
                                        </div>
                                    </div>
                                    <div className={styles.agentDescription}>
                                        <EllipsisTooltip text={agentData.description || '描述'}  maxWidth={500} />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className={styles.rightPanelContent}>
                            <AgentPreview agentId={agentData.id} />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AgentEditPage;
