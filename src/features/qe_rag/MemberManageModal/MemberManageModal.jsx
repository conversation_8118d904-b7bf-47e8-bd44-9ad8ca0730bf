import { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON>, Ta<PERSON>, Button, Table, Tag, Form, Input, message, Pagination } from 'antd';
import { UserOutlined, TeamOutlined, AuditOutlined, PlusOutlined } from '@ant-design/icons';
import {getJoinedList,getJoinedGroupinfo, getGroups, checkList, applyYes, applyNo} from 'COMMON/api/qe_rag/workgroup';
import ApplyGroupDialog from './components/ApplyGroupDialog';
import CreateGroupDialog from './components/CreateGroupDialog';
import styles from './MemberManageModal.module.less';

const { TabPane } = Tabs;

// 工具函数
const splitMembers = (memberStr) => {
    if (!memberStr) return [];
    return memberStr.split(';').filter(Boolean);
};

const formatDate = (dateStr) => {
    if (!dateStr) return '';
    return new Date(dateStr).toLocaleDateString();
};

// 我加入的组表格组件
const MyGroupsTable = () => {
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 });

    const fetchData = useCallback(async (params = {}) => {
        setLoading(true);
        try {
            const res = await getJoinedGroupinfo({
                page: pagination.current,
                pageSize: pagination.pageSize,
                ...params
            });
            // Ensure we always set an array for the table data
            const dataArray = res?.items || [];
            setData(dataArray);
            setPagination((prev) => ({ ...prev, total: res?.total || dataArray.length }));
        } finally {
            setLoading(false);
        }
    }, [pagination.current, pagination.pageSize]);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    const columns = [
        { title: '工作组名称', dataIndex: 'name', key: 'name' },
        {
            title: '管理员',
            dataIndex: 'admin',
            key: 'admin',
            render: (admin) => (
                <div>
                    {splitMembers(admin).map((member, index) => (
                        <Tag key={index} size="small">
                            {member}
                        </Tag>
                    ))}
                </div>
            )
        },
        {
            title: '成员',
            dataIndex: 'member',
            key: 'member',
            render: (member) => (
                <div>
                    {splitMembers(member).map((m, index) => (
                        <Tag key={index} size="small">
                            {m}
                        </Tag>
                    ))}
                </div>
            )
        },
        { title: '创建者', dataIndex: 'createUser', key: 'createUser' },
        { title: '事业群', dataIndex: 'business', key: 'business' },
        { title: '经理', dataIndex: 'manager', key: 'manager' },
        {
            title: '创建时间',
            dataIndex: 'createAt',
            key: 'createAt',
            render: formatDate
        }
    ];

    return (
        <>
            <Table
                columns={columns}
                dataSource={Array.isArray(data) ? data : []}
                loading={loading}
                pagination={false}
                rowKey="id"
            />
            <div style={{ marginTop: 16, textAlign: 'center' }}>
                <Pagination
                    current={pagination.current}
                    pageSize={pagination.pageSize}
                    total={pagination.total}
                    showSizeChanger
                    showQuickJumper
                    showTotal={(total) => `共 ${total} 条`}
                    onChange={(page, pageSize) => {
                        setPagination((prev) => ({ ...prev, current: page, pageSize }));
                    }}
                />
            </div>
        </>
    );
};

// 全部团队表格组件
const AllGroupsTable = ({ refreshTrigger }) => {
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 });
    const [filterForm] = Form.useForm();
    const [applyDialogVisible, setApplyDialogVisible] = useState(false);
    const [currentApplyGroup, setCurrentApplyGroup] = useState(null);

    const fetchData = useCallback(async (params = {}) => {
        setLoading(true);
        try {
            const res = await getGroups({
                page: pagination.current,
                size: pagination.pageSize,
                ...params
            });
            setData(res.items || []);
            setPagination((prev) => ({ ...prev, total: res.total || res.items?.length || 0 }));
        } catch (error) {
            console.error('获取全部团队数据失败:', error);
            message.error('获取数据失败');
        } finally {
            setLoading(false);
        }
    }, [pagination.current, pagination.pageSize]);

    useEffect(() => {
        fetchData();
    }, [fetchData, refreshTrigger]);

    const handleFilter = () => {
        const values = filterForm.getFieldsValue();
        fetchData(values);
    };

    const handleApply = (record) => {
        const admins = splitMembers(record.admin);
        if (admins.length === 0) {
            message.error('该工作组没有管理员，无法申请');
            return;
        }
        
        setCurrentApplyGroup({
            id: record.id,
            name: record.name,
            admins: admins
        });
        setApplyDialogVisible(true);
    };

    const handleApplySuccess = () => {
        message.success('申请已提交，等待审核');
    };

    const columns = [
        { title: '工作组名称', dataIndex: 'name', key: 'name' },
        {
            title: '管理员',
            dataIndex: 'admin',
            key: 'admin',
            render: (admin) => (
                <div>
                    {splitMembers(admin).map((member, index) => (
                        <Tag key={index} size="small">
                            {member}
                        </Tag>
                    ))}
                </div>
            )
        },
        {
            title: '成员',
            dataIndex: 'member',
            key: 'member',
            render: (member) => (
                <div>
                    {splitMembers(member).map((m, index) => (
                        <Tag key={index} size="small">
                            {m}
                        </Tag>
                    ))}
                </div>
            )
        },
        { title: '创建者', dataIndex: 'createUser', key: 'createUser' },
        { title: '事业群', dataIndex: 'business', key: 'business' },
        { title: '经理', dataIndex: 'manager', key: 'manager' },
        {
            title: '操作',
            key: 'action',
            render: (_, record) => (
                <Button size="small" type="primary" onClick={() => handleApply(record)}>
                    申请
                </Button>
            )
        }
    ];

    return (
        <>
            <Form form={filterForm} layout="inline" style={{ margin: '16px 0' }}>
                <Form.Item name="name" label="工作组名称">
                    <Input placeholder="输入工作组名称" />
                </Form.Item>
                <Form.Item name="manager" label="经理">
                    <Input placeholder="输入经理名称" />
                </Form.Item>
                <Form.Item name="business" label="事业群名称">
                    <Input placeholder="请输入事业群名称" />
                </Form.Item>
                <Form.Item>
                    <Button type="primary" onClick={handleFilter}>
                        查询
                    </Button>
                </Form.Item>
            </Form>
            <Table
                columns={columns}
                dataSource={Array.isArray(data) ? data : []}
                loading={loading}
                pagination={false}
                rowKey="id"
            />
            <div style={{ marginTop: 16, textAlign: 'center' }}>
                <Pagination
                    current={pagination.current}
                    pageSize={pagination.pageSize}
                    total={pagination.total}
                    showSizeChanger
                    showQuickJumper
                    showTotal={(total) => `共 ${total} 条`}
                    onChange={(page, pageSize) => {
                        setPagination((prev) => ({ ...prev, current: page, pageSize }));
                    }}
                />
            </div>
            
            <ApplyGroupDialog
                visible={applyDialogVisible}
                onCancel={() => setApplyDialogVisible(false)}
                groupId={currentApplyGroup?.id}
                groupName={currentApplyGroup?.name}
                admins={currentApplyGroup?.admins || []}
                onSubmitSuccess={handleApplySuccess}
            />
        </>
    );
};

// 待审批列表组件
const PendingApprovalTable = () => {
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(false);

    const fetchData = useCallback(async () => {
        setLoading(true);
        try {
            const res = await checkList({});
            // Ensure we always set an array for the table data
            const dataArray = Array.isArray(res) ? res :
                             Array.isArray(res?.data) ? res.data :
                             Array.isArray(res?.items) ? res.items : [];
            setData(dataArray);
        } catch (error) {
            console.error('获取待审批数据失败:', error);
            message.error('获取数据失败');
            setData([]); // Ensure we set an empty array on error
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    const handleApprove = async (record) => {
        try {
            await applyYes({
                id: record.id,
                workGroupId: record.workGroupId,
                role: record.role,
                userName: record.username
            });
            message.success('审批成功');
            fetchData();
        } catch (error) {
            console.error('审批失败:', error);
            message.error('审批失败');
        }
    };

    const handleReject = async (record) => {
        try {
            await applyNo({
                id: record.id,
                workGroupId: record.workGroupId,
                role: record.role,
                userName: record.username
            });
            message.success('拒绝成功');
            fetchData();
        } catch (error) {
            console.error('拒绝失败:', error);
            message.error('拒绝失败');
        }
    };

    const columns = [
        { title: '组ID', dataIndex: 'workGroupId', key: 'workGroupId' },
        { title: '组名称', dataIndex: 'workGroupName', key: 'workGroupName' },
        {
            title: '申请角色',
            dataIndex: 'role',
            key: 'role',
            render: (role) => (
                <Tag color={role === 1 ? 'blue' : 'green'}>{role === 1 ? '管理员' : '成员'}</Tag>
            )
        },
        { title: '申请原因', dataIndex: 'reason', key: 'reason' },
        { title: '申请人', dataIndex: 'userName', key: 'userName' },
        {
            title: '操作',
            key: 'action',
            render: (_, record) => (
                <div>
                    <Button
                        size="small"
                        type="primary"
                        onClick={() => handleApprove(record)}
                        style={{ marginRight: 8 }}
                    >
                        同意
                    </Button>
                    <Button size="small" danger onClick={() => handleReject(record)}>
                        拒绝
                    </Button>
                </div>
            )
        }
    ];

    return (
        <Table
            columns={columns}
            dataSource={Array.isArray(data) ? data : []}
            loading={loading}
            pagination={false}
            rowKey="id"
        />
    );
};

const MemberManageModal = ({ visible, onCancel }) => {
    const [activeTab, setActiveTab] = useState('1');
    const [createDialogVisible, setCreateDialogVisible] = useState(false);
    const [refreshTrigger, setRefreshTrigger] = useState(0);

    const handleCreateSuccess = () => {
        message.success('工作组创建成功');
        setRefreshTrigger(prev => prev + 1);
    };

    const modalTitle = (
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>成员管理</span>
            <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={() => setCreateDialogVisible(true)}
                style={{ marginRight: 24 }}
            >
                新增群组
            </Button>
        </div>
    );

    return (
        <>
            <Modal
                title={modalTitle}
                open={visible}
                onCancel={onCancel}
                footer={null}
                width={1200}
                className={styles.memberManageModal}
            >
                <Tabs activeKey={activeTab} onChange={setActiveTab} className={styles.groupTabs}>
                    <TabPane
                        tab={
                            <div className={styles.tabLabel}>
                                <UserOutlined className={styles.tabIcon} />
                                <span className={styles.tabText}>我加入的</span>
                            </div>
                        }
                        key="1"
                    >
                        {activeTab === '1' && <MyGroupsTable />}
                    </TabPane>
                    <TabPane
                        tab={
                            <div className={styles.tabLabel}>
                                <TeamOutlined className={styles.tabIcon} />
                                <span className={styles.tabText}>全部团队</span>
                            </div>
                        }
                        key="2"
                    >
                        {activeTab === '2' && <AllGroupsTable refreshTrigger={refreshTrigger} />}
                    </TabPane>
                    <TabPane
                        tab={
                            <div className={styles.tabLabel}>
                                <AuditOutlined className={styles.tabIcon} />
                                <span className={styles.tabText}>待审批列表</span>
                            </div>
                        }
                        key="3"
                    >
                        {activeTab === '3' && <PendingApprovalTable />}
                    </TabPane>
                </Tabs>
            </Modal>
            
            <CreateGroupDialog
                visible={createDialogVisible}
                onCancel={() => setCreateDialogVisible(false)}
                onSubmitSuccess={handleCreateSuccess}
            />
        </>
    );
};

export default MemberManageModal;