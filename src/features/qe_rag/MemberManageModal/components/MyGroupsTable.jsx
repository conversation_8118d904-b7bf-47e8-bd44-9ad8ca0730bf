import React, { useState, useEffect } from 'react';
import { Table, Tag, Button, message, Pagination } from 'antd';
import { CopyOutlined } from '@ant-design/icons';
import GroupFormDialog from './GroupFormDialog';
import { formatDate, splitMembers } from '../../../../pages/qe_rag/utils';
import styles from './common.module.less';


const MyGroupsTable = () => {
    const [tableData, setTableData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [dialogVisible, setDialogVisible] = useState(false);
    const [isEditMode, setIsEditMode] = useState(false);
    const [currentFormData, setCurrentFormData] = useState(getInitialFormData());
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
    });

    function getInitialFormData() {
        return {
            id: 0,
            name: '',
            createUser: '', // 从用户store获取
            business: '',
            manager: '',
            member: '',
            admin: '',
            managerArray: [],
            memberArray: [],
            adminArray: [],
            createAt: '',
            updateAt: '',
        };
    }

    const getTableData = async () => {
        setLoading(true);
        try {
            const params = {
                page: pagination.current,
                pageSize: pagination.pageSize,
            };
            // 调用API获取我加入的组数据
            // const res = await GroupApi.getGroup(params);
            // setTableData(res.data.items || []);
            // setPagination(prev => ({ ...prev, total: res.data.total || 0 }));
            
            // 模拟数据
            setTableData([]);
        } catch (error) {
            message.error('获取数据失败');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        getTableData();
    }, [pagination.current, pagination.pageSize]);

    const handleSizeChange = (current, size) => {
        setPagination(prev => ({
            ...prev,
            current: 1,
            pageSize: size
        }));
    };

    const handleCurrentChange = (page) => {
        setPagination(prev => ({
            ...prev,
            current: page
        }));
    };

    const canEdit = (row) => {
        // 从用户store获取当前用户名
        const username = ''; // userStore.username;
        return row.admin?.split(';').includes(username);
    };

    const handleEdit = (row) => {
        if (!canEdit(row)) {
            message.warning('您没有权限编辑该群组');
            return;
        }
        setIsEditMode(true);
        setCurrentFormData({
            ...row,
            managerArray: row.manager,
            memberArray: row.member?.split(';').filter(Boolean) || [],
            adminArray: row.admin?.split(';').filter(Boolean) || [],
        });
        setDialogVisible(true);
    };

    const copyToken = (token) => {
        navigator.clipboard.writeText(token).then(() => {
            message.success('Token已复制到剪贴板');
        }).catch((err) => {
            console.error('复制失败:', err);
            message.error('复制失败，请手动复制');
        });
    };

    const columns = [
        {
            title: '工作组ID',
            dataIndex: 'id',
            key: 'id',
            ellipsis: true,
        },
        {
            title: '工作组名称',
            dataIndex: 'name',
            key: 'name',
            ellipsis: true,
        },
        {
            title: '管理员1',
            dataIndex: 'manager',
            key: 'manager',
            width: 200,
            ellipsis: true,
            render: (admin) => (
                <div className={styles.memberTags}>
                    {splitMembers(admin).map((member, index) => (
                        <Tag key={index} size="small" className={styles.memberTag}>
                            {member}
                        </Tag>
                    ))}
                </div>
            ),
        },
        {
            title: '成员',
            dataIndex: 'member',
            key: 'member',
            width: 200,
            ellipsis: true,
            render: (member) => (
                <div className={styles.memberTags}>
                    {splitMembers(member).map((memberName, index) => (
                        <Tag key={index} size="small" className={styles.memberTag}>
                            {memberName}
                        </Tag>
                    ))}
                </div>
            ),
        },
        {
            title: '创建者',
            dataIndex: 'createUser',
            key: 'createUser',
            ellipsis: true,
        },
        {
            title: '事业群',
            dataIndex: 'business',
            key: 'business',
            ellipsis: true,
        },
        {
            title: '经理',
            dataIndex: 'manager',
            key: 'manager',
            ellipsis: true,
        },
        {
            title: 'Token',
            dataIndex: 'token',
            key: 'token',
            width: 100,
            ellipsis: true,
            render: (token) => (
                <div className={styles.tokenDisplay}>
                    <span className={styles.tokenText}>
                        {token ? token.slice(0, 8) + '...' : ''}
                    </span>
                    {token && (
                        <Button
                            size="small"
                            type="text"
                            icon={<CopyOutlined />}
                            onClick={(e) => {
                                e.stopPropagation();
                                copyToken(token);
                            }}
                            title="复制Token"
                            className={styles.copyBtn}
                        />
                    )}
                </div>
            ),
        },
        {
            title: '创建时间',
            dataIndex: 'createAt',
            key: 'createAt',
            ellipsis: true,
            render: (createAt) => formatDate(createAt),
        },
        {
            title: '更新时间',
            dataIndex: 'updateAt',
            key: 'updateAt',
            ellipsis: true,
            render: (updateAt) => formatDate(updateAt),
        },
        {
            title: '操作',
            key: 'action',
            fixed: 'right',
            width: 100,
            render: (_, record) => (
                <Button
                    size="small"
                    type="primary"
                    onClick={() => handleEdit(record)}
                    disabled={!canEdit(record)}
                >
                    编辑
                </Button>
            ),
        },
    ];

    return (
        <div className={styles.myGroupsTable}>
            <Table
                columns={columns}
                dataSource={tableData}
                loading={loading}
                pagination={false}
                rowKey="id"
                scroll={{ x: 1200 }}
            />
            
            <div className={styles.paginationContainer}>
                <Pagination
                    current={pagination.current}
                    pageSize={pagination.pageSize}
                    total={pagination.total}
                    showSizeChanger
                    showQuickJumper
                    showTotal={(total) => `共 ${total} 条`}
                    pageSizeOptions={['10', '20', '50', '100']}
                    onChange={handleCurrentChange}
                    onShowSizeChange={handleSizeChange}
                />
            </div>

            <GroupFormDialog
                visible={dialogVisible}
                onCancel={() => setDialogVisible(false)}
                formData={currentFormData}
                isEdit={isEditMode}
                onSubmitSuccess={getTableData}
            />
        </div>
    );
};

export default MyGroupsTable;