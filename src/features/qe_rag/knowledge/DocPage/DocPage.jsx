import React, { useState, useEffect, useCallback, useRef } from 'react';
import { debounce, cloneDeep, merge, find, isEmpty } from 'lodash';
import { useNavigate } from 'umi';
import {
    Card,
    Form,
    Input,
    Select,
    Button,
    Tag,
    Row,
    Col,
    message,
    Space,
    Avatar,
    List,
    Upload,
    Tabs,
    Divider,
    Typography,
    Popover,
    Dropdown,
    Modal,
    Pagination,
    Badge,
    Empty,
    Switch,
    Descriptions,
    Spin,
    Tooltip
} from 'antd';
import {
    SearchOutlined,
    PlusOutlined,
    BookOutlined,
    UserOutlined,
    CalendarOutlined,
    FileTextOutlined,
    InboxOutlined,
    CloudUploadOutlined,
    SettingOutlined,
    MoreOutlined,
    DeleteOutlined,
    FilterOutlined,
    UploadOutlined,
    StopOutlined,
    PlayCircleOutlined
} from '@ant-design/icons';
import { stringifyUrl } from 'query-string';
import { connectModel } from 'COMMON/middleware';
import { getQueryParams } from 'COMMON/utils/utils';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import ragModel from 'COMMON/models/qe_rag/ragModel';
import AddDocumentModal from './components/AddDocumentModal/AddDocumentModal';
import DocumentDetailForm from './components/DocumentDetailForm/DocumentDetailForm';
import {
    DOCUMENT_STATUSES,
    DEFAULT_BOOK,
    DEFAULT_PAGE_SIZE,
    DEFAULT_CURRENT_PAGE,
    DEFAULT_CUT_METHOD,
    DEFAULT_MAX_TOKENS,
    DEFAULT_FILTERS,
    DEFAULT_FILTER_QUERY,
    STATUS_COLOR_MAP,
    DEFAULT_SAFE_ITEM,
    MODAL_TEXTS,
    MESSAGE_TEXTS,
    PLACEHOLDER_TEXTS,
    LABEL_TEXTS,
    EMPTY_TEXTS
} from './const';
import styles from './DocPage.module.less';
import {
    getDocuments,
    deleteDocument,
    updateDocumentStatus,
    updateDocument,
    createDocument,
    getDocumentDetail
} from 'COMMON/api/qe_rag/document';
import { getBook } from 'COMMON/api/qe_rag/book';

const { Option } = Select;
const { Dragger } = Upload;
const { TabPane } = Tabs;
const { Title, Text } = Typography;

const DocumentManagePage = ({
    statuses = DOCUMENT_STATUSES,
    onRouteChange,
    allWorkGroupList,
    username
}) => {
    // 直接从URL解析docId
    const getDocIdFromUrl = () => {
        const urlParams = new URLSearchParams(window.location.search);

        // 处理hash路由中的参数，兼容错误格式（如 ?=knowledgeId=7&docId=65）
        let hashParamString = window.location.hash.split('?')[1] || '';

        // 修复可能的格式错误：如果参数字符串以 = 开头，去掉开头的 =
        if (hashParamString.startsWith('=')) {
            hashParamString = hashParamString.substring(1);
        }

        const hashParams = new URLSearchParams(hashParamString);
        return urlParams.get('docId') || hashParams.get('docId');
    };

    const [urlDocId, setUrlDocId] = useState(getDocIdFromUrl());
    // 筛选状态
    const [filters, setFilters] = useState(DEFAULT_FILTERS);
  

    const [selectedDoc, setSelectedDoc] = useState(null);
    const [searchText, setSearchText] = useState('');
    const [addModalVisible, setAddModalVisible] = useState(false);
    const [cutMethod, setCutMethod] = useState(DEFAULT_CUT_METHOD);
    const [maxTokens, setMaxTokens] = useState(DEFAULT_MAX_TOKENS);
    const [shouldSelectFirst, setShouldSelectFirst] = useState(false);

    // 数据状态管理
    const [items, setItems] = useState([]);
    const [total, setTotal] = useState(0);
    const [loading, setLoading] = useState(false);
    const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
    const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
    const [searchQuery, setSearchQuery] = useState('');
    const [filterQuery, setFilterQuery] = useState(DEFAULT_FILTER_QUERY);

    // 动态分页相关状态和ref
    const listContainerRef = useRef(null);
    const [dynamicPageSize, setDynamicPageSize] = useState(DEFAULT_PAGE_SIZE);
    const [containerHeight, setContainerHeight] = useState(0);
     // 文档详情状态
    const [documentDetail, setDocumentDetail] = useState(null);
    const [detailLoading, setDetailLoading] = useState(false);
    const [editLoading, setEditLoading] = useState(false);

    // 模型列表状态
    // const [modelsLoading, setModelsLoading] = useState(false);

    // 知识库默认值状态
    const [knowledgeBaseDefaults, setKnowledgeBaseDefaults] = useState(null);
    const [defaultsLoading, setDefaultsLoading] = useState(false);

    // 在状态管理部分添加切片分页状态
    const [chunkPagination, setChunkPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0
    });

    const query = getQueryParams();
    const navigate = useNavigate();
      // 监听URL变化
    useEffect(() => {
        const handleUrlChange = () => {
            const newDocId = getDocIdFromUrl();
            setUrlDocId(newDocId);
        };

        // 监听popstate事件（浏览器前进后退）
        window.addEventListener('popstate', handleUrlChange);
        // 监听hashchange事件（hash路由变化）
        window.addEventListener('hashchange', handleUrlChange);

        return () => {
            window.removeEventListener('popstate', handleUrlChange);
            window.removeEventListener('hashchange', handleUrlChange);
        };
    }, []);
    // 计算动态分页大小
    const calculateDynamicPageSize = useCallback(() => {
        if (!listContainerRef.current) {
            return DEFAULT_PAGE_SIZE;
        }

        const container = listContainerRef.current;
        const containerHeight = container.clientHeight;

        // 每个列表项的实际高度（根据样式分析）
        // List.Item的padding是5px上下，marginBottom是4px，内容高度约30px
        const itemHeight = 44; // 实际测量约44px每个item

        // 计算能容纳的最大item数量，至少保证显示5个
        const maxItems = Math.max(5, Math.floor(containerHeight / itemHeight));

        return maxItems;
    }, [pageSize]);

    // 初始化时计算动态分页大小（只执行一次）
    useEffect(() => {
        const initializePageSize = () => {
            if (!listContainerRef.current) {
                return;
            }

            const newPageSize = calculateDynamicPageSize();

            if (newPageSize > 0 && newPageSize !== pageSize) {
                setDynamicPageSize(newPageSize);
                setPageSize(newPageSize);
                setCurrentPage(1);
            }
        };

        // 延迟执行，确保DOM已经渲染完成
        const timer = setTimeout(initializePageSize, 800);

        return () => {
            clearTimeout(timer);
        };
    }, []); // 空依赖数组，只在组件挂载时执行一次

   
    // 计算当前页显示的切片数据
    const getCurrentChunks = () => {
        if (!documentDetail?.contentList) {
            return [];
        }
        const { current, pageSize } = chunkPagination;
        const startIndex = (current - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        return documentDetail.contentList.slice(startIndex, endIndex);
    };

    // 处理切片分页变化
    const handleChunkPageChange = (page, size) => {
        setChunkPagination((prev) => ({
            ...prev,
            current: page,
            pageSize: size || prev.pageSize
        }));
    };

    // 更新切片总数（在获取文档详情后调用）
    useEffect(() => {
        if (documentDetail?.contentList) {
            setChunkPagination((prev) => ({
                ...prev,
                total: documentDetail.contentList.length,
                current: 1 // 重置到第一页
            }));
        }
    }, [documentDetail?.contentList]);

    // 独立的数据获取函数
    const fetchDocuments = useCallback(
        async (overrideParams = {}) => {
            setLoading(true);
            try {
                const queryParams = {
                    title: filterQuery.title || searchQuery,
                    owner: filterQuery.owner,
                    status: filterQuery.status,
                    id: filterQuery.id,
                    knowledgeBaseId: query?.knowledgeId || 64,
                    page: currentPage,
                    size: pageSize,
                    ...overrideParams
                };
                console.log('fetchDocuments queryParams:', knowledgeBaseDefaults);
                const result = await getDocuments(queryParams);

                if (result && result.items) {
                    setItems(result.items);
                    setTotal(result.total);
                } else {
                    setItems([]);
                    setTotal(0);
                }
            } catch (error) {
                message.error('获取文档数据失败: ' + (error.message || '未知错误'));
                setItems([]);
                setTotal(0);
            } finally {
                setLoading(false);
            }
        },
        [searchQuery, filterQuery, currentPage, pageSize, knowledgeBaseDefaults?.id]
    );

    // 获取嵌入模型列表
    // const fetchModels = useCallback(async () => {
    //     setModelsLoading(true);
    //     try {
    //         // 使用新的API调用方式
    //         const requestData = {
    //             isDelete: 0, // 0表示未删除的模型
    //             type: 0 // 0表示嵌入模型，1表示聊天模型
    //         };
    //         const response = await getEmbeddingModels(requestData);

    //         if (response) {
    //             setModels(response || []);
    //         } else {
    //             setModels([]);
    //         }
    //     } catch (error) {
    //         message.error('获取模型列表失败: ' + (error.message || '未知错误'));
    //         setModels([]);
    //     } finally {
    //         setModelsLoading(false);
    //     }
    // }, []);

    // 获取知识库默认值
    const fetchKnowledgeBaseDefaults = useCallback(async () => {
        if (!query?.knowledgeId) {
            return;
        }
        setDefaultsLoading(true);
        try {
            const res = await getBook(query?.knowledgeId);
            const embeddingRule = JSON.parse(res.embeddingRule || '{}');
            const defaults = {
                ...res, // 保留所有原始数据
                parseId: res.parseId || 1,
                delimiter: embeddingRule.delimiter || ['。', '！'],
                chunkTokenNum: embeddingRule.chunkTokenNum || 600,
                embeddingModelId: res.embeddingModelId,
                cronOpen: false,
                cronExpression: ''
            };

            setKnowledgeBaseDefaults(defaults);
        } finally {
            setDefaultsLoading(false);
        }
    }, [query?.knowledgeId]);

    // 初始化数据
    useEffect(() => {
        fetchDocuments();
        fetchKnowledgeBaseDefaults();
    }, [fetchDocuments, fetchKnowledgeBaseDefaults]);

    // 监听分页、搜索、筛选变化
    useEffect(() => {
        fetchDocuments();
    }, [currentPage, pageSize, searchQuery, filterQuery]);

    // 根据路由中的docId和items变化来处理文档选中
    useEffect(() => {
        if (!items || items.length === 0) {
            setSelectedDoc(null);
            return;
        }

        // 如果有路由中的docId，尝试选中对应的文档
        if (urlDocId) {
            const targetDoc = items.find((item) => item.id.toString() === urlDocId.toString());
            if (targetDoc) {
                // 只有当前选中的文档不是目标文档时才更新
                if (selectedDoc?.id !== targetDoc.id) {
                    setSelectedDoc(targetDoc);
                    // 获取文档详情
                    fetchDocumentDetail(targetDoc.id);
                }
                return;
            }
        }

        // 如果需要自动选中第一个文档（新建文档后）
        if (shouldSelectFirst) {
            handleDocumentSelect(items[0]);
            setShouldSelectFirst(false); // 重置状态
            return;
        }

        // 如果没有路由docId，清空选中状态
        if (!urlDocId) {
            setSelectedDoc(null);
        }
    }, [items, urlDocId, shouldSelectFirst]);

    // 防抖优化搜索
    const debouncedSearch = useCallback(
        debounce((searchValue) => {
            setSearchQuery(searchValue);
            setCurrentPage(1);
        }, 500),
        []
    );

    // 当搜索文本变化时，使用防抖处理
    useEffect(() => {
        debouncedSearch(searchText);
        // 清理函数
        return () => {
            debouncedSearch.cancel();
        };
    }, [searchText, debouncedSearch]);



    // 使用lodash优化筛选条件更新
    const updateFilter = useCallback((key, value) => {
        setFilters((prev) => merge(cloneDeep(prev), { [key]: value }));
    }, []);

    // 重置筛选条件
    const resetFilters = useCallback(() => {
        setFilters(cloneDeep(DEFAULT_FILTERS));
        setFilterQuery(cloneDeep(DEFAULT_FILTER_QUERY));
        setCurrentPage(1);
    }, []);

    // 处理返回按钮点击
    const handleGoBack = ()=>{
        navigate(
            stringifyUrl({
                url: '/qe_rag/knowledge',
            })
        );
    }

    // 使用lodash检查是否有筛选条件被选中
    const hasActiveFilters = () => {
        const activeFilters = {
            ...filterQuery,
            ...filters
        };
        // 移除空值和null值，然后检查是否还有有效值
        return !isEmpty(
            Object.values(activeFilters).filter(
                (value) => value !== '' && value !== null && value !== undefined
            )
        );
    };

    // 获取状态标签
    const getStatusTag = (status, _isSelected = false) => {
        const statusInfo = statuses.find((item) => item.key === status);
        const statusText = statusInfo ? statusInfo.value : '';

        switch (status) {
            case 1:
            case 5:
                return <Tag color="orange">{statusText}</Tag>;
            case 2:
                return <Tag color="green">{statusText}</Tag>;
            case 3:
                return <Tag color="red">{statusText}</Tag>;
            case 4:
                return <Tag color="default">{statusText}</Tag>;
            default:
                return <Tag>{statusText}</Tag>;
        }
    };

    // 处理文档详情保存
    const handleDocumentSave = async (updateData) => {
        try {
            setEditLoading(true);
            await updateDocument(updateData);

            message.success('文档更新成功');

            // 重新获取文档详情
            await fetchDocumentDetail(documentDetail.id);

            await fetchDocuments();
        } catch (error) {
            message.error('保存失败: ' + (error.message || '未知错误'));
            throw error;
        } finally {
            setEditLoading(false);
        }
    };

    // 获取文档详情
    const fetchDocumentDetail = useCallback(async (docId) => {
        setDetailLoading(true);
        try {
            const response = await getDocumentDetail({ id: docId });
                setDocumentDetail(response);
        }  finally {
            setDetailLoading(false);
        }
    }, []);

    // 处理文档选择
    const handleDocumentSelect = (doc) => {
        // 检查是否已经选中了同一个文档，避免重复处理
        if (selectedDoc?.id === doc.id) {
            return;
        }

        setSelectedDoc(doc);

        // 获取文档详情
        fetchDocumentDetail(doc.id);

        // 更新路由，添加 docId 参数，保留 knowledgeId
        // const currentUrl = new URL(window.location.href);
        // const currentParams = Object.fromEntries(currentUrl.searchParams.entries());

        // 使用 navigate 更新路由，保留原有参数并添加 docId
        navigate(
            stringifyUrl({
                url: '/qe_rag/knowledge/docs',
                query: {
                    ...query,
                    docId: doc.id
                }
            })
        );

        // 触发路由变化回调（通知Vue组件更新路由）
        onRouteChange?.(doc.id);
    };

    // 处理删除文档
    const handleDeleteDocument = async (docId, docTitle) => {
        try {
            const body = {
                id: docId,
                owner: 'xushixuan01'
            };
            await deleteDocument(body);

            message.success(`文档 "${docTitle}" 删除成功`);

            // 如果删除的是当前选中的文档，清空选中状态并更新路由
            if (selectedDoc?.id === docId) {
                setSelectedDoc(null);
                setDocumentDetail(null);

                // 清除路由中的 docId 参数，保留其他参数（如 knowledgeId）
                const currentUrl = new URL(window.location.href);
                const currentParams = Object.fromEntries(currentUrl.searchParams.entries());
                delete currentParams.docId;

                navigate(
                    stringifyUrl({
                        url: '/qe_rag/knowledge/docs',
                        query: currentParams
                    })
                );
            }

            await fetchDocuments();
        } catch (error) {
            message.error(`删除文档 "${docTitle}" 失败: ${error.message || '未知错误'}`);
        }
    };

    // 处理文档状态更新（禁用/启用）
    const handleUpdateDocumentStatus = async (docId, docTitle, newStatus, actionName) => {
        try {
            const body = {
                id: docId,
                // owner: username,
                owner: 'xushixuan01',
                status: newStatus
            };
            // 调用状态更新API
            await updateDocumentStatus(body);

            // 显示成功消息
            message.success(`文档 "${docTitle}" ${actionName}成功`);

            // 状态更新成功后刷新列表
            await fetchDocuments();
        } catch (error) {
            message.error(`${actionName}文档 "${docTitle}" 失败: ${error.message || '未知错误'}`);
        }
    };

    // 处理文档创建成功
    const handleAddDocumentSuccess = async (newDocId) => {
        setAddModalVisible(false);

        // 刷新文档列表
        await fetchDocuments();

        // 等待一个短暂的时间确保数据更新完成
        await new Promise((resolve) => setTimeout(resolve, 100));

        // 设置标志，在数据更新后自动选中第一个文档
        setShouldSelectFirst(true);
    };

    // 处理分页变化
    const handlePageChange = (page, size) => {
        setCurrentPage(page);
        if (size && size !== pageSize) {
            setPageSize(size);
        }
    };
    // 处理添加文档
    const handleAddDocument = () => {
        setAddModalVisible(true);
    };

    // 格式化工作组信息显示
    const formatGroupInfo = (groupId) => {
        if (!groupId) {
            return '-';
        }

        // 在工作组列表中查找对应的工作组
        const group = allWorkGroupList?.find((g) => g.id === groupId);

        return group ? (
            <span>
                <span
                    style={{
                        // backgroundColor: '#e9f4fe',
                        backgroundColor: '#6fa3f5a6',
                        color: 'white',
                        padding: '2px 6px',
                        borderRadius: '4px',
                        fontSize: '12px',
                        fontWeight: '500',
                        marginRight: '8px',
                        border: '1px solid #6fa3f5'
                    }}
                >
                    {groupId}
                </span>
                {group.name}
            </span>
        ) : (
            '-'
        );
    };

    // 使用独立的状态数据
    const displayItems = items;
    const displayTotal = total;
    const displayLoading = loading;
    const displayCurrentPage = currentPage;
    const displayPageSize = pageSize;

    return (
        <>
            <style>
                {`
        .document-item:hover {
          background-color: #f5f5f5 !important;
        }
        .document-item-selected:hover {
          background-color: #1677ff !important;
        }
      `}
            </style>
            <div style={{ padding: '0px 10px', background: '#f0f2f5', height: 'calc(100vh - 64px)' }}>
                {/* 顶部知识库信息 */}
                <Card
                    style={{
                        marginBottom: 10,
                        background: 'linear-gradient(135deg, #4F8FF7 0%, #3875f0 100%)',
                        border: 'none'
                    }}
                >
                    <Row align="middle" justify="space-between">
                        <Col>
                            <Space size="large">
                                {/* 返回按钮 */}
                                <Button
                                    type="text"
                                    icon={
                                        <span style={{ color: 'white', fontSize: '18px' }}>←</span>
                                    }
                                    onClick={handleGoBack}
                                    style={{ color: 'white', fontSize: '16px', padding: '4px 8px' }}
                                >
                                    返回
                                </Button>
                                <div>
                                    <div
                                        style={{
                                            display: 'flex',
                                            alignItems: 'center'
                                        }}
                                    >
                                        <div>
                                            <Tag color="blue">知识库</Tag>
                                        </div>

                                        <div style={{ color: 'white', margin: 0 }}>{knowledgeBaseDefaults?.name}</div>
                                    </div>

                                    <Space style={{ marginTop: 8 }}>
                                        <Text style={{ color: 'rgba(255,255,255,0.8)' }}>
                                            <UserOutlined /> 工作组：{formatGroupInfo(knowledgeBaseDefaults?.groupId)}
                                        </Text>
                                        <Text
                                            style={{
                                                color: 'rgba(255,255,255,0.8)',
                                                marginLeft: 5
                                            }}
                                        >
                                            <CalendarOutlined /> 创建时间: {knowledgeBaseDefaults?.createAt}
                                        </Text>
                                    </Space>
                                </div>
                            </Space>
                        </Col>
                        <Col>
                            <Button
                                type="primary"
                                size="large"
                                icon={<PlusOutlined />}
                                onClick={handleAddDocument}
                                style={{
                                    backgroundColor: 'rgba(255,255,255,0.3)',
                                    borderColor: 'rgba(255,255,255,0.6)'
                                }}
                            >
                                添加文档
                            </Button>
                        </Col>
                    </Row>
                </Card>

                {/* 主要内容区域 */}
                <Row gutter={8} style={{ height: 'calc(100vh - 170px)' }}>
                    {/* 左侧文档列表 */}
                    <Col span={8}>
                        <Card
                            title={
                                <div
                                    style={{
                                        display: 'flex',
                                        gap: '8px',
                                        alignItems: 'center',
                                        width: '100%'
                                    }}
                                >
                                    {/* 搜索框 */}
                                    <div style={{ flex: 1 }}>
                                        <Input
                                            placeholder="输入文档名称..."
                                            value={searchText}
                                            onChange={(e) => setSearchText(e.target.value)}
                                            prefix={<SearchOutlined />}
                                            style={{ width: '100%' }}
                                        />
                                    </div>

                                    {/* 筛选按钮 */}
                                    <Popover
                                        getPopupContainer={() => document.body}
                                        content={
                                            <div style={{ minWidth: '300px' }}>
                                                <Form size="small">
                                                    <Row gutter={[8, 8]} align="middle">
                                                        <Col span={5}>
                                                            <span style={{ fontSize: '14px' }}>
                                                                文档ID
                                                            </span>
                                                        </Col>
                                                        <Col span={19}>
                                                            <Input
                                                                placeholder="请输入文档ID"
                                                                value={filters.documentId}
                                                                onChange={(e) =>
                                                                    updateFilter(
                                                                        'documentId',
                                                                        e.target.value
                                                                    )
                                                                }
                                                                allowClear
                                                            />
                                                        </Col>
                                                    </Row>
                                                    <Row
                                                        gutter={[8, 5]}
                                                        style={{ marginTop: '8px' }}
                                                        align="middle"
                                                    >
                                                        <Col span={5}>
                                                            <span style={{ fontSize: '14px' }}>
                                                                创建人
                                                            </span>
                                                        </Col>
                                                        <Col span={19}>
                                                            <Input
                                                                placeholder="请输入创建人"
                                                                value={filters.creator}
                                                                onChange={(e) =>
                                                                    updateFilter(
                                                                        'creator',
                                                                        e.target.value
                                                                    )
                                                                }
                                                                allowClear
                                                            />
                                                        </Col>
                                                    </Row>

                                                    <Row
                                                        gutter={[8, 8]}
                                                        style={{ marginTop: '8px' }}
                                                        align="middle"
                                                    >
                                                        <Col span={5}>
                                                            <span style={{ fontSize: '14px' }}>
                                                                &nbsp; &nbsp;状态
                                                            </span>
                                                        </Col>
                                                        <Col span={19}>
                                                            <Select
                                                                placeholder="请选择状态"
                                                                value={filters.status}
                                                                onChange={(value) =>
                                                                    updateFilter('status', value)
                                                                }
                                                                onClear={() =>
                                                                    updateFilter('status', null)
                                                                }
                                                                style={{ width: '100%' }}
                                                                allowClear
                                                                getPopupContainer={() =>
                                                                    document.body
                                                                }
                                                            >
                                                                {statuses.map((status) => (
                                                                    <Option
                                                                        key={status.key}
                                                                        value={status.key}
                                                                    >
                                                                        {status.value}
                                                                    </Option>
                                                                ))}
                                                            </Select>
                                                        </Col>
                                                    </Row>

                                                    <div
                                                        style={{
                                                            display: 'flex',
                                                            gap: '8px',
                                                            justifyContent: 'flex-end',
                                                            marginTop: '16px',
                                                            paddingTop: '8px',
                                                            borderTop: '1px solid #f0f0f0'
                                                        }}
                                                    >
                                                        <Button size="small" onClick={resetFilters}>
                                                            重置
                                                        </Button>
                                                        <Button
                                                            type="primary"
                                                            size="small"
                                                            onClick={() => {
                                                                console.log('应用筛选:', filters);
                                                                setFilterQuery({
                                                                    title:
                                                                        filters.documentName || '',
                                                                    owner: filters.creator || '',
                                                                    status: filters.status || '',
                                                                    id: filters.documentId || ''
                                                                });
                                                                setCurrentPage(1); // 筛选时重置到第一页
                                                            }}
                                                        >
                                                            确定
                                                        </Button>
                                                    </div>
                                                </Form>
                                            </div>
                                        }
                                        title="筛选条件"
                                        trigger="click"
                                        placement="right"
                                        zIndex={1000}
                                        styles={{
                                            content: { maxWidth: '350px' }
                                        }}
                                    >
                                        <Badge dot={hasActiveFilters()}>
                                            <FilterOutlined
                                                style={{
                                                    cursor: 'pointer',
                                                    fontSize: '16px',
                                                    color: '#777777'
                                                }}
                                            />
                                        </Badge>
                                    </Popover>
                                </div>
                            }
                            style={{
                                height: '100%',
                                width: '100%',
                                display: 'flex',
                                flexDirection: 'column'
                            }}
                            styles={{
                                header: {
                                    padding: '16px 16px 8px 16px'
                                },
                                body: {
                                    padding: '8px 16px 16px 16px',
                                    flex: 1,
                                    overflow: 'hidden',
                                    display: 'flex',
                                    flexDirection: 'column'
                                }
                            }}
                        >
                            <div
                                style={{
                                    flex: 1,
                                    display: 'flex',
                                    flexDirection: 'column',
                                    minHeight: 0
                                }}
                            >
                                {/* 文档列表 */}
                                <div
                                    ref={listContainerRef}
                                    style={{
                                        flex: 1,
                                        overflowY: 'auto',
                                        minHeight: 0
                                    }}
                                >
                                    {/* 表头 */}
                                    <div
                                        style={{
                                            display: 'flex',
                                            padding: '8px 20px',
                                            backgroundColor: '#fafafa',
                                            borderBottom: '1px solid #f0f0f0',
                                            fontWeight: '500',
                                            fontSize: '14px',
                                            color: '#666',
                                            position: 'sticky',
                                            top: 0,
                                            zIndex: 1
                                        }}
                                    >
                                        <div style={{ width: '40px', textAlign: 'center' }}>ID</div>
                                        <div style={{ flex: 1 }}>文档名</div>
                                        <div style={{ width: '60px', textAlign: 'center' }}>
                                            类型
                                        </div>
                                        <div style={{ width: '80px', textAlign: 'center' }}>
                                            状态
                                        </div>
                                        <div style={{ width: '40px' }}></div>
                                    </div>

                                    <List
                                        loading={displayLoading}
                                        dataSource={displayItems}
                                        renderItem={(item, index) => {
                                            const safeItem = {
                                                id: item.id || `item-${index}`,
                                                title: item.title || '未命名文档',
                                                type: item.type || '未知类型',
                                                status: item.status || 1,
                                                owner: item.owner || '未知',
                                                createAt: item.createAt || '未知时间'
                                            };

                                            return (
                                                <List.Item
                                                    key={safeItem.id}
                                                    onClick={() => handleDocumentSelect(safeItem)}
                                                    className={
                                                        selectedDoc?.id === safeItem.id
                                                            ? 'document-item-selected'
                                                            : 'document-item'
                                                    }
                                                    style={{
                                                        cursor: 'pointer',
                                                        backgroundColor:
                                                            selectedDoc?.id === safeItem.id
                                                                ? '#1677ff'
                                                                : 'transparent',
                                                        color:
                                                            selectedDoc?.id === safeItem.id
                                                                ? '#ffffff'
                                                                : '#333333',
                                                        padding: '5px 20px',
                                                        borderRadius: '8px',
                                                        marginBottom: '4px',
                                                        border: 'none',
                                                        transition: 'all 0.2s ease',
                                                        boxShadow:
                                                            selectedDoc?.id === safeItem.id
                                                                ? '0 2px 8px rgba(22, 119, 255, 0.3)'
                                                                : 'none'
                                                    }}
                                                >
                                                    <div
                                                        style={{
                                                            display: 'flex',
                                                            justifyContent: 'space-between',
                                                            alignItems: 'center',
                                                            width: '100%'
                                                        }}
                                                    >
                                                        <div style={{ flex: 1 }}>
                                                            <List.Item.Meta
                                                                title={
                                                                    <Tooltip
                                                                        title={safeItem.title}
                                                                        placement="top"
                                                                    >
                                                                        <div
                                                                            style={{
                                                                                color:
                                                                                    selectedDoc?.id ===
                                                                                    safeItem.id
                                                                                        ? '#ffffff'
                                                                                        : '#333333',
                                                                                fontWeight:
                                                                                    selectedDoc?.id ===
                                                                                    safeItem.id
                                                                                        ? '500'
                                                                                        : '400',
                                                                                overflow: 'hidden',
                                                                                textOverflow:
                                                                                    'ellipsis',
                                                                                whiteSpace:
                                                                                    'nowrap',
                                                                                maxWidth: '150px' // 限制最大宽度
                                                                            }}
                                                                        >
                                                                            <span
                                                                                style={{
                                                                                    backgroundColor:
                                                                                        '#f5f5f5',
                                                                                    color: '#666',
                                                                                    padding:
                                                                                        '2px 6px',
                                                                                    borderRadius:
                                                                                        '4px',
                                                                                    fontSize:
                                                                                        '12px',
                                                                                    fontWeight:
                                                                                        '500',
                                                                                    marginRight:
                                                                                        '8px'
                                                                                }}
                                                                            >
                                                                                {safeItem.id}
                                                                            </span>
                                                                            {safeItem.title}
                                                                        </div>
                                                                    </Tooltip>
                                                                }
                                                            />
                                                        </div>

                                                        {/* 右侧状态、Tag和菜单 */}
                                                        <div
                                                            style={{
                                                                display: 'flex',
                                                                alignItems: 'center',
                                                                gap: '8px'
                                                            }}
                                                        >
                                                            <Tag
                                                                color={'blue'}
                                                                style={{
                                                                    minWidth: '50px',
                                                                    textAlign: 'center',
                                                                    display: 'inline-block'
                                                                }}
                                                            >
                                                                {safeItem.type}
                                                            </Tag>
                                                            {getStatusTag(
                                                                safeItem.status,
                                                                selectedDoc?.id === safeItem.id
                                                            )}

                                                            <Dropdown
                                                                menu={{
                                                                    items: [
                                                                        // 禁用选项 - 只有切片成功(status=2)的文档才显示
                                                                        ...(safeItem.status === 2
                                                                            ? [
                                                                                  {
                                                                                      key: 'disable',
                                                                                      label: (
                                                                                          <span
                                                                                              style={{
                                                                                                  color: '#faad14'
                                                                                              }}
                                                                                          >
                                                                                              <StopOutlined
                                                                                                  style={{
                                                                                                      marginRight:
                                                                                                          '8px'
                                                                                                  }}
                                                                                              />
                                                                                              禁用
                                                                                          </span>
                                                                                      ),
                                                                                      onClick:
                                                                                          () => {
                                                                                              handleUpdateDocumentStatus(
                                                                                                  safeItem.id,
                                                                                                  safeItem.title,
                                                                                                  4, // 状态4表示已禁用
                                                                                                  '禁用'
                                                                                              );
                                                                                          }
                                                                                  }
                                                                              ]
                                                                            : []),
                                                                        // 启用选项 - 只有已禁用(status=4)的文档才显示
                                                                        ...(safeItem.status === 4
                                                                            ? [
                                                                                  {
                                                                                      key: 'enable',
                                                                                      label: (
                                                                                          <span
                                                                                              style={{
                                                                                                  color: '#52c41a'
                                                                                              }}
                                                                                          >
                                                                                              <PlayCircleOutlined
                                                                                                  style={{
                                                                                                      marginRight:
                                                                                                          '8px'
                                                                                                  }}
                                                                                              />
                                                                                              启用
                                                                                          </span>
                                                                                      ),
                                                                                      onClick:
                                                                                          () => {
                                                                                              handleUpdateDocumentStatus(
                                                                                                  safeItem.id,
                                                                                                  safeItem.title,
                                                                                                  2, // 状态2表示切片成功（启用）
                                                                                                  '启用'
                                                                                              );
                                                                                          }
                                                                                  }
                                                                              ]
                                                                            : []),
                                                                        // 删除选项 - 所有文档都显示
                                                                        {
                                                                            key: 'delete',
                                                                            label: (
                                                                                <span
                                                                                    style={{
                                                                                        color: '#ff4d4f'
                                                                                    }}
                                                                                >
                                                                                    <DeleteOutlined
                                                                                        style={{
                                                                                            marginRight:
                                                                                                '8px'
                                                                                        }}
                                                                                    />
                                                                                    删除
                                                                                </span>
                                                                            ),
                                                                            onClick: () => {
                                                                                Modal.confirm({
                                                                                    title: MODAL_TEXTS.DELETE_TITLE,
                                                                                    content:
                                                                                        MODAL_TEXTS.DELETE_CONTENT_TEMPLATE(
                                                                                            safeItem.title
                                                                                        ),
                                                                                    okText: MODAL_TEXTS.OK_TEXT,
                                                                                    cancelText:
                                                                                        MODAL_TEXTS.CANCEL_TEXT,
                                                                                    okType: 'danger',
                                                                                    onOk: () => {
                                                                                        handleDeleteDocument(
                                                                                            safeItem.id,
                                                                                            safeItem.title
                                                                                        );
                                                                                    }
                                                                                });
                                                                            }
                                                                        }
                                                                    ]
                                                                }}
                                                                trigger={['click']}
                                                                placement="bottomRight"
                                                                getPopupContainer={() =>
                                                                    document.body
                                                                }
                                                            >
                                                                <Button
                                                                    type="text"
                                                                    size="small"
                                                                    icon={
                                                                        <MoreOutlined
                                                                            style={{
                                                                                color:
                                                                                    selectedDoc?.id ===
                                                                                    safeItem.id
                                                                                        ? '#ffffff'
                                                                                        : '#999999'
                                                                            }}
                                                                        />
                                                                    }
                                                                    onClick={(e) =>
                                                                        e.stopPropagation()
                                                                    }
                                                                />
                                                            </Dropdown>
                                                        </div>
                                                    </div>
                                                </List.Item>
                                            );
                                        }}
                                    />
                                </div>

                                {/* 分页 - 只有多页时才显示 */}
                                {displayTotal > displayPageSize && (
                                    <div
                                        style={{
                                            display: 'flex',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            marginTop: '16px',
                                            paddingTop: '16px'
                                        }}
                                    >
                                        <Pagination
                                            current={displayCurrentPage}
                                            pageSize={displayPageSize}
                                            total={displayTotal}
                                            onChange={handlePageChange}
                                            simple
                                            // showSizeChanger
                                            // showQuickJumper
                                            // showTotal={(total, range) =>
                                            //     `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                                            // }
                                            size="small"
                                        />
                                    </div>
                                )}
                            </div>
                        </Card>
                    </Col>

                    {/* 右侧详情面板 */}
                    <Col span={16}>
                        <Card
                            style={{
                                height: '100%',
                                display: 'flex',
                                flexDirection: 'column'
                            }}
                        >
                            {selectedDoc ? (
                                <Tabs
                                    defaultActiveKey="overview"
                                    size="small"
                                    style={{
                                        height: '100%',
                                        display: 'flex',
                                        flexDirection: 'column'
                                    }}
                                    tabBarStyle={{
                                        margin: 0,
                                        paddingLeft: '20px',
                                        paddingRight: '20px'
                                    }}
                                >
                                    <TabPane tab="概览" key="overview">
                                        <div
                                            style={{
                                                padding: '20px',
                                                height: 'calc(100vh - 230px)',
                                                display: 'flex',
                                                flexDirection: 'column',
                                                overflow: 'auto'
                                            }}
                                        >
                                            <Spin spinning={detailLoading}>
                                                {documentDetail ? (
                                                    <div
                                                        style={{
                                                            display: 'flex',
                                                            flexDirection: 'column',
                                                            height: '100%'
                                                        }}
                                                    >
                                                        {/* 基本信息 */}
                                                        <div style={{ flexShrink: 0 }}>
                                                            <Title
                                                                level={5}
                                                                style={{ marginBottom: '16px' }}
                                                            >
                                                                基本信息
                                                            </Title>

                                                            <Descriptions
                                                                title=""
                                                                style={{
                                                                    backgroundColor: '#fafafa',
                                                                    padding: '16px',
                                                                    borderRadius: '6px'
                                                                }}
                                                            >
                                                                <Descriptions.Item label="文档ID">
                                                                    {documentDetail.id}
                                                                </Descriptions.Item>

                                                                <Descriptions.Item
                                                                    label="文档标题"
                                                                    style={{
                                                                        whiteSpace: 'nowrap',
                                                                        overflow: 'hidden',
                                                                        textOverflow: 'ellipsis'
                                                                    }}
                                                                >
                                                                    <Tooltip
                                                                        title={documentDetail.title}
                                                                    >
                                                                        <div
                                                                            style={{
                                                                                whiteSpace:
                                                                                    'nowrap',
                                                                                overflow: 'hidden',
                                                                                textOverflow:
                                                                                    'ellipsis'
                                                                            }}
                                                                        >
                                                                            {documentDetail.title}
                                                                        </div>
                                                                    </Tooltip>
                                                                </Descriptions.Item>

                                                                <Descriptions.Item label="文档类型">
                                                                    {documentDetail.type}
                                                                </Descriptions.Item>
                                                                <Descriptions.Item label="创建人">
                                                                    {documentDetail.owner}
                                                                </Descriptions.Item>
                                                                <Descriptions.Item label="创建时间">
                                                                    {documentDetail.createAt}
                                                                </Descriptions.Item>
                                                                <Descriptions.Item label="更新时间">
                                                                    {documentDetail.updateAt}
                                                                </Descriptions.Item>
                                                            </Descriptions>
                                                        </div>

                                                        {/* 处理消息 */}
                                                        <div
                                                            style={{
                                                                marginBottom: '24px',
                                                                flexShrink: 0
                                                            }}
                                                        >
                                                            <Title
                                                                level={5}
                                                                style={{ margin: '16px 0' }}
                                                            >
                                                                处理消息
                                                            </Title>
                                                            <div
                                                                style={{
                                                                    padding: '12px',
                                                                    backgroundColor: '#fff2e8',
                                                                    borderRadius: '6px',
                                                                    border: '1px solid #ffbb96'
                                                                }}
                                                            >
                                                                <Text type="warning">
                                                                    {documentDetail.message}
                                                                </Text>
                                                            </div>
                                                        </div>

                                                        {/* 附加信息 */}
                                                        {documentDetail.metadata && (
                                                            <div
                                                                style={{
                                                                    marginBottom: '24px',
                                                                    flexShrink: 0
                                                                }}
                                                            >
                                                                <Title
                                                                    level={5}
                                                                    style={{ marginBottom: '16px' }}
                                                                >
                                                                    附加信息
                                                                </Title>
                                                                <div
                                                                    style={{
                                                                        padding: '12px',
                                                                        backgroundColor: '#f5f5f5',
                                                                        borderRadius: '6px'
                                                                    }}
                                                                >
                                                                    <Text>
                                                                        {documentDetail.metadata}
                                                                    </Text>
                                                                </div>
                                                            </div>
                                                        )}

                                                        {/* 切片信息 */}
                                                        <div
                                                            style={{
                                                                display: 'flex',
                                                                flexDirection: 'column',
                                                                flex: 1,
                                                                minHeight: 0
                                                            }}
                                                        >
                                                            <Title
                                                                level={5}
                                                                style={{
                                                                    margin: '16px 0',
                                                                    flexShrink: 0
                                                                }}
                                                            >
                                                                切片信息
                                                                {documentDetail.contentList &&
                                                                    documentDetail.contentList
                                                                        .length > 0 && (
                                                                        <Text
                                                                            type="secondary"
                                                                            style={{
                                                                                fontSize: '12px',
                                                                                marginLeft: '8px'
                                                                            }}
                                                                        >
                                                                            （共{' '}
                                                                            {
                                                                                documentDetail
                                                                                    .contentList
                                                                                    .length
                                                                            }{' '}
                                                                            条）
                                                                        </Text>
                                                                    )}
                                                            </Title>
                                                            {documentDetail.contentList &&
                                                            documentDetail.contentList.length >
                                                                0 ? (
                                                                <>
                                                                    <div
                                                                        style={{
                                                                            flex: 1,
                                                                            overflowY: 'auto',
                                                                            border: '1px solid #f0f0f0',
                                                                            borderRadius: '6px',
                                                                            padding: '8px',
                                                                            minHeight: 0,
                                                                            maxHeight: '100%'
                                                                        }}
                                                                    >
                                                                        <Space
                                                                            direction="vertical"
                                                                            size="small"
                                                                            style={{
                                                                                width: '100%'
                                                                            }}
                                                                        >
                                                                            {getCurrentChunks().map(
                                                                                (chunk, index) => {
                                                                                    const actualIndex =
                                                                                        (chunkPagination.current -
                                                                                            1) *
                                                                                            chunkPagination.pageSize +
                                                                                        index;
                                                                                    return (
                                                                                        <div
                                                                                            key={
                                                                                                actualIndex
                                                                                            }
                                                                                            style={{
                                                                                                padding:
                                                                                                    '12px',
                                                                                                backgroundColor:
                                                                                                    '#fafafa',
                                                                                                borderRadius:
                                                                                                    '6px',
                                                                                                border: '1px solid #f0f0f0'
                                                                                            }}
                                                                                        >
                                                                                            <div
                                                                                                style={{
                                                                                                    fontSize:
                                                                                                        '12px',
                                                                                                    color: '#999',
                                                                                                    marginBottom:
                                                                                                        '8px'
                                                                                                }}
                                                                                            >
                                                                                                切片 #
                                                                                                {actualIndex +
                                                                                                    1}
                                                                                            </div>
                                                                                            <div
                                                                                                style={{
                                                                                                    fontSize:
                                                                                                        '14px',
                                                                                                    lineHeight:
                                                                                                        '1.6',
                                                                                                    whiteSpace:
                                                                                                        'pre-wrap',
                                                                                                    wordBreak:
                                                                                                        'break-word'
                                                                                                }}
                                                                                            >
                                                                                                {
                                                                                                    chunk.content
                                                                                                }
                                                                                            </div>
                                                                                        </div>
                                                                                    );
                                                                                }
                                                                            )}
                                                                        </Space>
                                                                    </div>

                                                                    {/* 切片分页 - 只有多页时才显示 */}
                                                                    {chunkPagination.total > chunkPagination.pageSize && (
                                                                        <div
                                                                            style={{
                                                                                display: 'flex',
                                                                                justifyContent: 'center',
                                                                                alignItems: 'center',
                                                                                marginTop: '16px',
                                                                                flexShrink: 0
                                                                            }}
                                                                        >
                                                                            <Pagination
                                                                                current={
                                                                                    chunkPagination.current
                                                                                }
                                                                                simple
                                                                                pageSize={
                                                                                    chunkPagination.pageSize
                                                                                }
                                                                                total={
                                                                                    chunkPagination.total
                                                                                }
                                                                                onChange={
                                                                                    handleChunkPageChange
                                                                                }
                                                                                size="small"
                                                                            />
                                                                        </div>
                                                                    )}
                                                                </>
                                                            ) : (
                                                                <Empty
                                                                    description={
                                                                        EMPTY_TEXTS.NO_SLICE_DATA
                                                                    }
                                                                />
                                                            )}
                                                        </div>
                                                    </div>
                                                ) : (
                                                    <Empty
                                                        description={
                                                            EMPTY_TEXTS.LOADING_DOCUMENT_DETAIL
                                                        }
                                                    />
                                                )}
                                            </Spin>
                                        </div>
                                    </TabPane>

                                    <TabPane tab="编辑" key="detail">
                                        <div
                                            style={{
                                                padding: '20px',
                                                height: 'calc(100vh - 260px)',
                                                overflowY: 'auto'
                                            }}
                                        >
                                            <Spin spinning={detailLoading}>
                                                <DocumentDetailForm
                                                    documentDetail={documentDetail}
                                                    onSave={handleDocumentSave}
                                                    loading={editLoading}
                                                />
                                            </Spin>
                                        </div>
                                    </TabPane>
                                </Tabs>
                            ) : (
                                <Empty
                                    image="https://gw.alipayobjects.com/zos/antfincdn/ZHrcdLPrvN/empty.svg"
                                    style={{ marginTop: '100px' }}
                                    description={<div>请选择一个文档查看详情</div>}
                                />
                            )}
                        </Card>
                    </Col>
                </Row>
            </div>

            {/* 添加文档弹窗 */}
            <AddDocumentModal
                visible={addModalVisible}
                onCancel={() => setAddModalVisible(false)}
                onSuccess={handleAddDocumentSuccess}
                loading={loading}
                knowledgeBaseId={knowledgeBaseDefaults?.id}
                defaultValues={knowledgeBaseDefaults}
                defaultsLoading={defaultsLoading}
            />
        </>
    );
};

export default connectModel([baseModel, commonModel, ragModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    workGroupList: state.common.rag.workGroupList,
    username: state.common.base.username,
    joinedWorkGroupList: state.common.rag.joinedWorkGroupList,
    allWorkGroupList: state.common.rag.allWorkGroupList,
    embeddingModelList: state.common.rag.embeddingModelList
}))(DocumentManagePage);