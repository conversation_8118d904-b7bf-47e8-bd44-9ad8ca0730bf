// 文档状态常量
export const DOCUMENT_STATUSES = [
	{ key: 1, value: '等待切片' },
	{ key: 2, value: '切片成功' },
	{ key: 3, value: '切片失败' },
	{ key: 4, value: '已被禁用' },
	{ key: 5, value: '切片中' },
];

// 默认知识库信息
export const DEFAULT_BOOK = {
	id: null,
	name: '',
	description: '',
	owner: '',
	createAt: '',
};

// 默认分页配置
export const DEFAULT_PAGE_SIZE = 10;
export const DEFAULT_CURRENT_PAGE = 1;


// 默认切片方法
export const DEFAULT_CUT_METHOD = '通用';

// 默认最大Token数
export const DEFAULT_MAX_TOKENS = 600;

// 默认筛选条件
export const DEFAULT_FILTERS = {
	documentId: '',
	documentName: '',
	creator: '',
	status: null,
};

// 默认查询条件
export const DEFAULT_FILTER_QUERY = {
	title: '',
	owner: '',
	status: '',
	id: '',
};

// 状态颜色映射
export const STATUS_COLOR_MAP = {
	1: 'orange', // 等待切片
	2: 'green', // 切片成功
	3: 'red', // 切片失败
	4: 'default', // 已被禁用
	5: 'orange', // 切片中
};

// 默认安全项目数据
export const DEFAULT_SAFE_ITEM = {
	title: '未命名文档',
	type: '未知类型',
	status: 1,
	owner: '未知',
	createAt: '未知时间',
};

// 文本内容 - 直接使用字符串字面量
// 模态框文本
// '确认删除'
// '确定要删除文档 "${title}" 吗？此操作不可恢复。'
// '确定'
// '取消'

// 消息文本
// '文档 "${title}" 删除成功'
// '删除文档 "${title}" 失败: ${error}'
// '文档 "${title}" ${action}成功'
// '${action}文档 "${title}" 失败: ${error}'
// '文档更新成功'
// '保存失败: ${error}'
// '获取文档详情失败: ${error}'
// '获取文档数据失败: ${error}'
// '获取模型列表失败: ${error}'
// '刷新文档列表失败，请手动刷新页面'
// '${fileName} 文件上传成功'
// '${fileName} 文件上传失败'

// 占位符文本
// '输入文档名称...'
// '请输入文档ID'
// '请输入文档名称'
// '请输入创建人'
// '请选择状态'

// 标签文本
// '文档ID'
// '文档名'
// '创建人'
// '状态'
// '筛选条件'
// '重置'
// '确定'
// '返回'
// '添加文档'
// '基本信息'
// '文档标题'
// '文档类型'
// '创建时间'
// '更新时间'
// '处理消息'
// '附加信息'
// '切片信息'
// '概览'
// '详情'
// '禁用'
// '启用'
// '删除'

// 空状态文本
// '暂无文档数据'
// '加载中...'
// '无法获取文档详情'
// '加载文档详情中...'
// '暂无切片数据'
// '请选择一个文档查看详情'

export const dict = {
	delimiters: [
		{
			label: '中文',
			options: [
				{ value: '。', label: '中文句号[。]' },
				{ value: '！', label: '中文感叹符[！]' },
				{ value: '？', label: '中文问号[？]' },
				{ value: '；', label: '中文分号[；]' },
			],
		},
		{
			label: '英文',
			options: [
				{ value: '.', label: '英文句号[.]' },
				{ value: '!', label: '英文感叹号[!]' },
				{ value: '?', label: '英文问号[?]' },
				{ value: ';', label: '英文分号[;]' },
			],
		},
		{
			label: '其他',
			options: [
				{ value: '\n', label: '换行符[\\n]' },
				{ value: '\n\n', label: '换行符*2[\\n\\n]' },
			],
		},
	],
	parserOptions: [
		{ label: '通用', value: 1 },
		{ label: '不切词', value: 2 },
		{ label: 'excel表格', value: 3 },
		{ label: '通用（增强）', value: 4 },
		{ label: 'code index', value: 5 },
	],
};
