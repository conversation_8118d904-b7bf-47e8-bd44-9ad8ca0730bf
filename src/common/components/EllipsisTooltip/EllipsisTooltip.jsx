import { useRef, useEffect, useState } from 'react';
import { Tooltip } from 'antd';
import styles from './EllipsisTooltip.module.less';

// 如果文本长度超过maxWidth，则显示省略号，并添加Tooltip提示
const EllipsisTooltip = ({ text, maxWidth = 150, maxLines = 1, defaultText = '未知', ...props }) => {
    const spanRef = useRef(null);
    const [isOverflow, setIsOverflow] = useState(false);

    // 监听text和maxLines的变化，当它们变化时，重新判断文本是否溢出
    useEffect(() => {
        const el = spanRef.current;
        if (el) {
            // 对于多行文本，检查scrollHeight是否大于clientHeight
            if (maxLines > 1) {
                setIsOverflow(el.scrollHeight > el.clientHeight);
            } else {
                // 单行文本检查scrollWidth
                setIsOverflow(el.scrollWidth > el.clientWidth);
            }
        }
    }, [text, maxLines]);

    // 根据maxLines决定使用的样式类
    const className = maxLines > 1 ? styles.ellipsisTextMultiLine : styles.ellipsisText;

    const content = (
        <span
            ref={spanRef}
            className={className}
            style={{
                maxWidth: `${maxWidth}px`,
                ...(maxLines > 1 && {
                    WebkitLineClamp: maxLines,
                    lineHeight: '1.4em',
                    maxHeight: `${maxLines * 1.4}em`
                })
            }}
            {...props}
        >
            {text ?? defaultText}
        </span>
    );

    // 文本溢出，则显示Tooltip提示
    return isOverflow ? <Tooltip title={text ?? defaultText}>{content}</Tooltip> : content;
};

export default EllipsisTooltip;
